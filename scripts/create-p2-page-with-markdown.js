#!/usr/bin/env node

/**
 * 创建页面菜单 p2 并在其中插入 markdown block
 */

import { spawn } from 'child_process';

let requestId = 1;

// MCP 客户端
class MCPClient {
  constructor(server) {
    this.server = server;
    this.pendingRequests = new Map();
    this.setupResponseHandler();
  }

  setupResponseHandler() {
    let buffer = '';
    this.server.stdout.on('data', (data) => {
      buffer += data.toString();
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      lines.forEach(line => {
        if (line.trim()) {
          try {
            const response = JSON.parse(line);
            this.handleResponse(response);
          } catch (e) {
            console.log('📥 Raw output:', line);
          }
        }
      });
    });

    this.server.stderr.on('data', (data) => {
      console.log('🔍 Server log:', data.toString());
    });
  }

  handleResponse(response) {
    console.log('📥 Response:', JSON.stringify(response, null, 2));

    if (response.id && this.pendingRequests.has(response.id)) {
      const { resolve, reject } = this.pendingRequests.get(response.id);
      this.pendingRequests.delete(response.id);

      if (response.error) {
        reject(new Error(response.error.message || 'Unknown error'));
      } else {
        resolve(response.result);
      }
    }
  }

  async sendRequest(method, params = {}) {
    const id = requestId++;
    const request = {
      jsonrpc: '2.0',
      id,
      method,
      params
    };

    return new Promise((resolve, reject) => {
      this.pendingRequests.set(id, { resolve, reject });

      this.server.stdin.write(JSON.stringify(request) + '\n');

      // 设置超时
      setTimeout(() => {
        if (this.pendingRequests.has(id)) {
          this.pendingRequests.delete(id);
          reject(new Error('Request timeout'));
        }
      }, 30000);
    });
  }

  close() {
    if (this.server) {
      this.server.kill();
    }
  }
}

async function main() {
  // 启动 MCP 服务器
  const server = spawn('node', [
    'dist/index.js',
    '--base-url', 'https://app.dev.orb.local/api',
    '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
    '--app', 'mcp_playground'
  ], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  const mcpClient = new MCPClient(server);

  try {
    // 等待服务器启动
    console.log('⏳ Waiting for server to start...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log('\n🚀 开始创建页面菜单 p2 并添加 markdown block...\n');

    // 1. 初始化
    console.log('📋 Step 1: Initialize MCP client');
    await mcpClient.sendRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: { name: 'create-p2-client', version: '1.0.0' }
    });

    // 2. 创建页面菜单 p2
    console.log('\n📋 Step 2: Create page menu "p2"');
    const createPageResult = await mcpClient.sendRequest('tools/call', {
      name: 'create_page_route',
      arguments: {
        title: 'p2',
        template: 'blank',
        icon: 'FileTextOutlined',
        enableTabs: true
      }
    });

    console.log('✅ Page created:', JSON.stringify(createPageResult, null, 2));

    // 从创建结果中提取页面的 schema UID
    let pageSchemaUid = null;
    let tabSchemaUid = null;
    
    if (createPageResult.content && createPageResult.content[0] && createPageResult.content[0].text) {
      const resultText = createPageResult.content[0].text;
      
      // 尝试从结果文本中提取 UID
      const pageUidMatch = resultText.match(/Page UID: ([a-zA-Z0-9_-]+)/);
      const tabUidMatch = resultText.match(/Tab UID: ([a-zA-Z0-9_-]+)/);
      
      if (pageUidMatch) pageSchemaUid = pageUidMatch[1];
      if (tabUidMatch) tabSchemaUid = tabUidMatch[1];
    }

    if (!tabSchemaUid) {
      console.error('❌ Could not extract tab schema UID from page creation result');
      return;
    }

    console.log(`📝 Using tab schema UID: ${tabSchemaUid}`);

    // 3. 获取页面结构以找到 Grid 容器
    console.log('\n📋 Step 3: Get page schema to find Grid container');
    const schemaResult = await mcpClient.sendRequest('tools/call', {
      name: 'get_page_schema',
      arguments: {
        schemaUid: tabSchemaUid
      }
    });

    console.log('📊 Page schema:', JSON.stringify(schemaResult, null, 2));

    // 从 schema 结果中查找 Grid 的 UID
    let gridUid = null;
    if (schemaResult.content && schemaResult.content[0] && schemaResult.content[0].text) {
      const schemaText = schemaResult.content[0].text;
      try {
        // 提取JSON部分 - 查找第一个 { 到最后一个 }
        const jsonStart = schemaText.indexOf('{');
        const jsonEnd = schemaText.lastIndexOf('}');
        if (jsonStart !== -1 && jsonEnd !== -1) {
          const jsonText = schemaText.substring(jsonStart, jsonEnd + 1);
          const schemaData = JSON.parse(jsonText);

          // 递归查找 Grid 组件
          function findGridUid(obj) {
            if (typeof obj !== 'object' || obj === null) return null;

            if (obj['x-component'] === 'Grid' && obj['x-uid']) {
              return obj['x-uid'];
            }

            for (const key in obj) {
              const result = findGridUid(obj[key]);
              if (result) return result;
            }

            return null;
          }

          gridUid = findGridUid(schemaData);
        }
      } catch (e) {
        console.error('❌ Failed to parse schema JSON:', e.message);
        console.error('Schema text:', schemaText);
      }
    }

    if (!gridUid) {
      console.error('❌ Could not find Grid UID in page schema');
      return;
    }

    console.log(`📦 Using Grid UID: ${gridUid}`);

    // 4. 在页面中添加 markdown block
    console.log('\n📋 Step 4: Add markdown block to page');
    const markdownResult = await mcpClient.sendRequest('tools/call', {
      name: 'add_markdown_block',
      arguments: {
        parentUid: gridUid,
        title: 'Welcome to p2',
        content: '# 欢迎来到 p2 页面\n\n这是一个使用 MCP 工具创建的 markdown 区块。\n\n## 功能特性\n\n- ✅ 支持 Markdown 语法\n- ✅ 可以编辑内容\n- ✅ 响应式设计\n\n## 下一步\n\n您可以继续添加更多区块来丰富页面内容。',
        position: 'beforeEnd'
      }
    });

    console.log('✅ Markdown block added:', JSON.stringify(markdownResult, null, 2));

    console.log('\n🎉 成功完成！页面菜单 p2 已创建并添加了 markdown block');
    console.log('🌐 您可以在 NocoBase 管理界面中查看新创建的页面');

  } catch (error) {
    console.error('❌ Error:', error);
    if (error.response) {
      console.error('Response:', error.response);
    }
  } finally {
    mcpClient.close();
  }
}

// 运行主函数
main().catch(console.error);
