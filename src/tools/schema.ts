import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { z } from "zod";
import { NocoBaseClient } from "../client.js";
import { createFormattedResponse, createFormattedErrorResponse } from "../utils/response-formatter.js";

export async function registerSchemaTools(server: McpServer, client: NocoBaseClient) {
  // Get collection schema
  server.registerTool(
    "get_collection_schema",
    {
      title: "Get Collection Schema",
      description: "Get the complete schema definition for a collection",
      inputSchema: {
        collection: z.string().describe("Name of the collection")
      }
    },
    async ({ collection }) => {
      try {
        const collectionData = await client.getCollection(collection);

        let schema = `Schema for collection '${collection}':\n\n`;
        schema += `Name: ${collectionData.name}\n`;
        schema += `Title: ${collectionData.title || 'No title'}\n`;
        schema += `Description: ${collectionData.description || 'No description'}\n`;
        schema += `Auto-generated ID: ${collectionData.autoGenId ? 'Yes' : 'No'}\n`;
        schema += `Timestamps: ${collectionData.createdAt ? 'Created' : ''}${collectionData.updatedAt ? (collectionData.createdAt ? ', Updated' : 'Updated') : ''}\n`;
        schema += `User tracking: ${collectionData.createdBy ? 'Created by' : ''}${collectionData.updatedBy ? (collectionData.createdBy ? ', Updated by' : 'Updated by') : ''}\n`;
        schema += `Hidden: ${collectionData.hidden ? 'Yes' : 'No'}\n`;
        schema += `Inherits: ${collectionData.inherit ? 'Yes' : 'No'}\n\n`;

        if (collectionData.fields && collectionData.fields.length > 0) {
          schema += `Fields (${collectionData.fields.length}):\n\n`;
          
          collectionData.fields.forEach((field, index) => {
            schema += `${index + 1}. ${field.name}\n`;
            schema += `   Type: ${field.type}\n`;
            schema += `   Interface: ${field.interface || 'None'}\n`;
            schema += `   Description: ${field.description || 'No description'}\n`;
            
            if (field.uiSchema) {
              schema += `   UI Schema:\n`;
              Object.entries(field.uiSchema).forEach(([key, value]) => {
                schema += `     ${key}: ${typeof value === 'object' ? JSON.stringify(value) : value}\n`;
              });
            }
            
            // Show other important field properties
            const otherProps = Object.entries(field).filter(([key]) => 
              !['key', 'name', 'type', 'interface', 'description', 'uiSchema', 'collectionName'].includes(key)
            );
            
            if (otherProps.length > 0) {
              schema += `   Other properties:\n`;
              otherProps.forEach(([key, value]) => {
                if (value !== null && value !== undefined) {
                  schema += `     ${key}: ${typeof value === 'object' ? JSON.stringify(value) : value}\n`;
                }
              });
            }
            
            schema += '\n';
          });
        } else {
          schema += 'No fields defined.\n';
        }

        return {
          content: [{
            type: "text",
            text: schema
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error getting schema for collection '${collection}': ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Get page schema structure (using getProperties for complete structure)
  server.registerTool(
    "analyze_page_schema",
    {
      title: "Analyze Page Schema Structure",
      description: "Get the complete UI schema structure for a page, showing all blocks and their layout",
      inputSchema: {
        schemaUid: z.string().describe("Schema UID of the page (e.g., 'cafh7yoyd6w')")
      }
    },
    async ({ schemaUid }) => {
      try {
        // 使用 getProperties 获取完整的页面结构，而不是 getPageSchema
        const schema = await client.getSchemaProperties(schemaUid);

        let response = `Page Schema Structure for '${schemaUid}':\n\n`;

        // Helper function to recursively analyze schema structure
        function analyzeSchema(obj: any, depth: number = 0, key?: string): string {
          const indent = '  '.repeat(depth);
          let result = '';

          if (obj && typeof obj === 'object') {
            // Show the key name if provided
            if (key) {
              result += `${indent}🔑 ${key}:\n`;
            }

            // Check for component type
            if (obj['x-component']) {
              const componentIcon = getComponentIcon(obj['x-component']);
              result += `${indent}${componentIcon} Component: ${obj['x-component']}\n`;

              if (obj['x-uid']) {
                result += `${indent}   📋 UID: ${obj['x-uid']}\n`;
              }

              if (obj.title) {
                result += `${indent}   🏷️  Title: ${obj.title}\n`;
              }

              if (obj['x-component-props']) {
                const props = obj['x-component-props'];
                if (Object.keys(props).length > 0) {
                  result += `${indent}   ⚙️  Props:\n`;
                  for (const [propKey, propValue] of Object.entries(props)) {
                    if (propValue !== null && propValue !== undefined) {
                      const displayValue = typeof propValue === 'object'
                        ? JSON.stringify(propValue).substring(0, 100) + (JSON.stringify(propValue).length > 100 ? '...' : '')
                        : String(propValue);
                      result += `${indent}      ${propKey}: ${displayValue}\n`;
                    }
                  }
                }
              }
            }

            // Check for decorator
            if (obj['x-decorator']) {
              result += `${indent}🎨 Decorator: ${obj['x-decorator']}\n`;
              if (obj['x-decorator-props']) {
                const props = obj['x-decorator-props'];
                if (Object.keys(props).length > 0) {
                  result += `${indent}   ⚙️  Decorator Props:\n`;
                  for (const [propKey, propValue] of Object.entries(props)) {
                    if (propValue !== null && propValue !== undefined) {
                      const displayValue = typeof propValue === 'object'
                        ? JSON.stringify(propValue).substring(0, 100) + (JSON.stringify(propValue).length > 100 ? '...' : '')
                        : String(propValue);
                      result += `${indent}      ${propKey}: ${displayValue}\n`;
                    }
                  }
                }
              }
            }

            // Check for collection binding
            if (obj['x-collection-field']) {
              result += `${indent}🗂️  Collection Field: ${obj['x-collection-field']}\n`;
            }

            // Check for data source
            if (obj['x-data-source']) {
              result += `${indent}📊 Data Source: ${obj['x-data-source']}\n`;
            }

            // Check for ACL action
            if (obj['x-acl-action']) {
              result += `${indent}🔐 ACL Action: ${obj['x-acl-action']}\n`;
            }

            // Check for action type
            if (obj['x-action']) {
              result += `${indent}⚡ Action: ${obj['x-action']}\n`;
            }

            // Check for settings
            if (obj['x-settings']) {
              result += `${indent}🛠️  Settings: ${obj['x-settings']}\n`;
            }

            // Check for initializer
            if (obj['x-initializer']) {
              result += `${indent}🚀 Initializer: ${obj['x-initializer']}\n`;
            }

            // Check for properties (child components)
            if (obj.properties && typeof obj.properties === 'object') {
              const propCount = Object.keys(obj.properties).length;
              result += `${indent}📁 Properties (${propCount}):\n`;
              for (const [propKey, propValue] of Object.entries(obj.properties)) {
                result += analyzeSchema(propValue, depth + 1, propKey);
              }
            }

            result += '\n';
          }

          return result;
        }

        // Helper function to get component icon
        function getComponentIcon(component: string): string {
          const iconMap: { [key: string]: string } = {
            'Page': '📄',
            'Grid': '🏗️',
            'Grid.Row': '📏',
            'Grid.Col': '📐',
            'CardItem': '🃏',
            'TableV2': '📊',
            'TableV2.Column': '📋',
            'FormV2': '📝',
            'FormItem': '📝',
            'CollectionField': '🏷️',
            'Action': '🔘',
            'Action.Link': '🔗',
            'ActionBar': '🎛️',
            'Markdown.Void': '📝',
            'Iframe': '🖼️',
            'ChartCardItem': '📈',
            'Details': '📋',
            'Tabs': '📑',
            'Tabs.TabPane': '📄',
            'Space': '🔲'
          };
          return iconMap[component] || '📦';
        }

        response += analyzeSchema(schema);

        return createFormattedResponse(
          `Page Schema Structure for '${schemaUid}':`,
          schema,
          'schema'
        );
      } catch (error) {
        return createFormattedErrorResponse(error);
      }
    }
  );

  // Get page schema (basic info only)
  server.registerTool(
    "get_page_schema",
    {
      title: "Get Page Schema (Basic Info)",
      description: "Get basic page schema information using getJsonSchema API",
      inputSchema: {
        schemaUid: z.string().describe("Schema UID of the page")
      }
    },
    async ({ schemaUid }) => {
      try {
        const schema = await client.getPageSchema(schemaUid);
        
        return createFormattedResponse(
          `Basic Page Schema for '${schemaUid}':`,
          schema,
          'schema'
        );
      } catch (error) {
        return createFormattedErrorResponse(error);
      }
    }
  );

  // Get schema properties (blocks) - enhanced version
  server.registerTool(
    "get_schema_properties",
    {
      title: "Get Schema Properties",
      description: "Get the properties (child blocks) of a specific schema component",
      inputSchema: {
        schemaUid: z.string().describe("Schema UID of the component")
      }
    },
    async ({ schemaUid }) => {
      try {
        const properties = await client.getSchemaProperties(schemaUid);

        let response = `Schema Properties for '${schemaUid}':\n\n`;

        if (properties && typeof properties === 'object') {
          for (const [key, value] of Object.entries(properties)) {
            response += `🔧 Property: ${key}\n`;
            if (value && typeof value === 'object') {
              const prop = value as any;
              if (prop['x-component']) {
                response += `   Component: ${prop['x-component']}\n`;
              }
              if (prop['x-decorator']) {
                response += `   Decorator: ${prop['x-decorator']}\n`;
              }
              if (prop['x-uid']) {
                response += `   UID: ${prop['x-uid']}\n`;
              }
              if (prop['x-collection-field']) {
                response += `   Collection Field: ${prop['x-collection-field']}\n`;
              }
              if (prop['x-data-source']) {
                response += `   Data Source: ${prop['x-data-source']}\n`;
              }
              if (prop.title) {
                response += `   Title: ${prop.title}\n`;
              }
              if (prop['x-component-props']) {
                response += `   Component Props: ${JSON.stringify(prop['x-component-props']).substring(0, 100)}...\n`;
              }
            }
            response += '\n';
          }
        } else {
          response += 'No properties found or invalid response.\n';
        }

        return createFormattedResponse(
          `Schema Properties for '${schemaUid}':`,
          properties,
          'properties'
        );
      } catch (error) {
        return createFormattedErrorResponse(error);
      }
    }
  );

  // Compare two schemas
  server.registerTool(
    "compare_schemas",
    {
      title: "Compare Two Schemas",
      description: "Compare two schemas to identify differences",
      inputSchema: {
        schemaUid1: z.string().describe("First schema UID to compare"),
        schemaUid2: z.string().describe("Second schema UID to compare")
      }
    },
    async ({ schemaUid1, schemaUid2 }) => {
      try {
        const [schema1, schema2] = await Promise.all([
          client.getSchemaProperties(schemaUid1),
          client.getSchemaProperties(schemaUid2)
        ]);

        const differences = findSchemaDifferences(schema1, schema2);

        return createFormattedResponse(
          `Schema Comparison Results:`,
          {
            schema1: { uid: schemaUid1, data: schema1 },
            schema2: { uid: schemaUid2, data: schema2 },
            differences
          },
          'comparison'
        );
      } catch (error) {
        return createFormattedErrorResponse(error);
      }
    }
  );

  // Get page structure summary
  server.registerTool(
    "get_page_structure_summary",
    {
      title: "Get Page Structure Summary",
      description: "Get a comprehensive summary of a page's structure including block count and component types",
      inputSchema: {
        schemaUid: z.string().describe("Schema UID of the page")
      }
    },
    async ({ schemaUid }) => {
      try {
        const summary = await client.getSchemaSummary(schemaUid);
        
        let response = `Page Structure Summary for '${schemaUid}':\n\n`;
        response += `📊 Content Status: ${summary.hasContent ? '✅ Has Content' : '❌ No Content'}\n`;
        response += `🔢 Block Count: ${summary.blockCount}\n`;
        response += `🏗️  Component Types: ${summary.componentTypes.length > 0 ? summary.componentTypes.join(', ') : 'None'}\n\n`;
        
        if (summary.basicInfo) {
          response += `📋 Basic Info:\n`;
          response += `   - Type: ${summary.basicInfo.type || 'Unknown'}\n`;
          response += `   - Component: ${summary.basicInfo['x-component'] || 'Unknown'}\n`;
          response += `   - Name: ${summary.basicInfo.name || 'Unknown'}\n`;
          response += `   - UID: ${summary.basicInfo['x-uid'] || 'Unknown'}\n`;
        }

        return createFormattedResponse(
          `Page Structure Summary for '${schemaUid}':`,
          summary,
          'summary'
        );
      } catch (error) {
        return createFormattedErrorResponse(error);
      }
    }
  );

  // Check if page has content
  server.registerTool(
    "check_page_content",
    {
      title: "Check Page Content Status",
      description: "Quickly check if a page has any content or blocks",
      inputSchema: {
        schemaUid: z.string().describe("Schema UID of the page to check")
      }
    },
    async ({ schemaUid }) => {
      try {
        const hasContent = await client.hasSchemaContent(schemaUid);
        
        return createFormattedResponse(
          `Page Content Check for '${schemaUid}':`,
          {
            schemaUid,
            hasContent,
            status: hasContent ? 'Has Content' : 'No Content',
            message: hasContent ? 'This page contains blocks and content' : 'This page is empty or has no visible content'
          },
          'content_check'
        );
      } catch (error) {
        return createFormattedErrorResponse(error);
      }
    }
  );
}

// Helper function to find differences between schemas
function findSchemaDifferences(schema1: any, schema2: any, path: string = ''): any[] {
  const differences: any[] = [];
  
  if (typeof schema1 !== typeof schema2) {
    differences.push({
      path,
      type: 'type_mismatch',
      value1: typeof schema1,
      value2: typeof schema2
    });
    return differences;
  }

  if (typeof schema1 !== 'object' || schema1 === null || schema2 === null) {
    if (schema1 !== schema2) {
      differences.push({
        path,
        type: 'value_mismatch',
        value1: schema1,
        value2: schema2
      });
    }
    return differences;
  }

  const keys1 = Object.keys(schema1);
  const keys2 = Object.keys(schema2);

  // Check for missing keys
  for (const key of keys1) {
    if (!keys2.includes(key)) {
      differences.push({
        path: path ? `${path}.${key}` : key,
        type: 'missing_in_schema2',
        value: schema1[key]
      });
    }
  }

  for (const key of keys2) {
    if (!keys1.includes(key)) {
      differences.push({
        path: path ? `${path}.${key}` : key,
        type: 'missing_in_schema1',
        value: schema2[key]
      });
    }
  }

  // Check common keys
  for (const key of keys1) {
    if (keys2.includes(key)) {
      const newPath = path ? `${path}.${key}` : key;
      differences.push(...findSchemaDifferences(schema1[key], schema2[key], newPath));
    }
  }

  return differences;
}
