#!/usr/bin/env node

/**
 * 调用 MCP 工具来获取路由信息并添加 markdown block
 */

import { NocoBaseClient } from './dist/client.js';
import { handleGetPageSchema, handleAddMarkdownBlock } from './dist/tools/blocks.js';

// 配置 NocoBase 客户端
const client = new NocoBaseClient({
  baseUrl: 'https://app.dev.orb.local/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
  app: 'mcp_playground'
});

async function main() {
  try {
    console.log('🔍 获取路由列表...');
    
    // 1. 获取路由列表
    const routes = await client.listRoutes({ tree: true });
    console.log('✅ 路由列表:', JSON.stringify(routes, null, 2));
    
    // 2. 找到 p1 路由
    const p1Route = routes.find(route => route.title === 'p1');
    if (!p1Route) {
      throw new Error('未找到 p1 路由');
    }
    
    console.log('✅ 找到 p1 路由:', JSON.stringify(p1Route, null, 2));
    
    // 3. 获取页面 schema
    console.log('🔍 获取页面 schema...');
    const pageSchemaResult = await handleGetPageSchema(client, { 
      schemaUid: p1Route.schemaUid 
    });
    
    console.log('✅ 页面 schema 获取成功');
    console.log(pageSchemaResult.content[0].text);
    
    // 4. 找到 tab 的 schema UID（这是正确的父容器）
    const tabRoute = p1Route.children && p1Route.children[0];
    if (!tabRoute) {
      throw new Error('未找到 tab 路由');
    }

    const tabUid = tabRoute.schemaUid;
    console.log('📋 找到 tab UID:', tabUid);
    console.log('📋 Tab 路由信息:', JSON.stringify(tabRoute, null, 2));

    // 5. 获取 tab 的 schema 来确认结构
    console.log('🔍 获取 tab schema...');
    const tabSchemaResult = await handleGetPageSchema(client, {
      schemaUid: tabUid
    });

    console.log('✅ Tab schema 获取成功');
    console.log(tabSchemaResult.content[0].text);

    // 6. 添加 Markdown block 到 tab 容器（使用修复后的模板）
    console.log('📝 添加 Markdown block 到 tab（使用修复后的模板）...');
    const markdownResult = await handleAddMarkdownBlock(client, {
      parentUid: tabUid,
      title: 'Welcome to p1 - Fixed',
      content: '# 🎉 Welcome to p1 Page (Fixed Version)\n\nThis is a **fixed** markdown block added via MCP tools!\n\n## ✨ New Features\n\n- ✅ **Fixed template structure**\n- ✅ Proper `x-decorator` usage\n- ✅ Correct component hierarchy\n- ✅ Added missing properties\n\n**Now it should display correctly!** 🚀\n\n---\n\n*This block was created using the corrected template that matches the manual creation pattern.*',
      position: 'beforeEnd'
    });
    
    console.log('✅ Markdown block 添加成功!');
    console.log(markdownResult.content[0].text);
    
  } catch (error) {
    console.error('❌ 错误:', error.message);
    console.error(error.stack);
  }
}

main();
