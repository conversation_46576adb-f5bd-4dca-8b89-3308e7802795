#!/usr/bin/env node

/**
 * 检查 tab schema 的当前状态
 */

import { NocoBaseClient } from './dist/client.js';
import { handleGetPageSchema } from './dist/tools/blocks.js';

// 配置 NocoBase 客户端
const client = new NocoBaseClient({
  baseUrl: 'https://app.dev.orb.local/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
  app: 'mcp_playground'
});

async function main() {
  try {
    console.log('🔍 检查 tab schema 状态...');
    
    // Tab UID
    const tabUid = 'szdigp6b1ug';
    
    // 获取 tab 的 schema
    const tabSchemaResult = await handleGetPageSchema(client, { 
      schemaUid: tabUid 
    });
    
    console.log('✅ Tab schema 获取成功');
    console.log(tabSchemaResult.content[0].text);
    
  } catch (error) {
    console.error('❌ 错误:', error.message);
    console.error(error.stack);
  }
}

main();
