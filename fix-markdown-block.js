#!/usr/bin/env node

/**
 * 修复 Markdown block 显示问题
 */

import { NocoBaseClient } from './dist/client.js';

// 配置 NocoBase 客户端
const client = new NocoBaseClient({
  baseUrl: 'https://app.dev.orb.local/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
  app: 'mcp_playground'
});

async function main() {
  try {
    console.log('🔧 修复 Markdown block 显示问题...');
    
    // 1. 删除我们之前添加的 Markdown block
    console.log('🗑️ 删除之前的 Markdown block...');
    try {
      await client.removeUISchema('11agv4i6dum'); // CardItem UID
      console.log('✅ 删除成功');
    } catch (error) {
      console.log('⚠️ 删除失败或不存在:', error.message);
    }
    
    // 2. 等待一下
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 3. 检查当前 tab schema
    console.log('🔍 检查当前 tab schema...');
    const tabUid = 'szdigp6b1ug';
    const tabSchema = await client.getUISchema(tabUid);
    console.log('当前 tab schema:', JSON.stringify(tabSchema, null, 2));
    
  } catch (error) {
    console.error('❌ 错误:', error.message);
    console.error(error.stack);
  }
}

main();
