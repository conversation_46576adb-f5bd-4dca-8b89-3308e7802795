#!/usr/bin/env node

/**
 * 调试页面结构，检查所有相关的 schema
 */

import { NocoBaseClient } from './dist/client.js';
import { handleGetPageSchema } from './dist/tools/blocks.js';

// 配置 NocoBase 客户端
const client = new NocoBaseClient({
  baseUrl: 'https://app.dev.orb.local/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
  app: 'mcp_playground'
});

async function main() {
  try {
    console.log('🔍 调试页面结构...');
    
    // 1. 获取路由信息
    const routes = await client.listRoutes({ tree: true });
    const p1Route = routes.find(route => route.title === 'p1');
    
    console.log('📋 P1 路由信息:');
    console.log('- Page UID:', p1Route.schemaUid);
    console.log('- Tab UID:', p1Route.children[0].schemaUid);
    console.log('- Enable Tabs:', p1Route.enableTabs);
    
    // 2. 检查页面 schema
    console.log('\n🔍 检查页面 schema...');
    const pageSchema = await handleGetPageSchema(client, { 
      schemaUid: p1Route.schemaUid 
    });
    console.log('页面 Schema:', pageSchema.content[0].text);
    
    // 3. 检查 tab schema
    console.log('\n🔍 检查 tab schema...');
    const tabSchema = await handleGetPageSchema(client, { 
      schemaUid: p1Route.children[0].schemaUid 
    });
    console.log('Tab Schema:', tabSchema.content[0].text);
    
    // 4. 尝试直接获取 UI Schema
    console.log('\n🔍 直接获取 UI Schema...');
    try {
      const uiSchema = await client.getUISchema(p1Route.children[0].schemaUid);
      console.log('UI Schema (直接):', JSON.stringify(uiSchema, null, 2));
    } catch (error) {
      console.log('获取 UI Schema 失败:', error.message);
    }
    
  } catch (error) {
    console.error('❌ 错误:', error.message);
    console.error(error.stack);
  }
}

main();
