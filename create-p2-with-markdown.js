#!/usr/bin/env node

/**
 * 使用 MCP 工具创建 p2 页面菜单并添加 markdown 区块
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { spawn } from 'child_process';

// MCP 客户端配置
class McpClient {
  constructor() {
    this.client = null;
    this.transport = null;
  }

  async connect() {
    // 启动 MCP 服务器进程
    const serverProcess = spawn('node', [
      'dist/index.js',
      '--base-url', 'http://nocobase.nocobasedocker.orb.local/api',
      '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTUwNTI2MDEsImV4cCI6MzMzMTI2NTI2MDF9.2IzOVJB0G2VqE2zQpsULLXUhEw87dmLYAtKGcwB_wyw',
      '--app', 'mcp_playground'
    ], {
      stdio: ['pipe', 'pipe', 'inherit']
    });

    // 创建传输层
    this.transport = new StdioClientTransport({
      reader: serverProcess.stdout,
      writer: serverProcess.stdin
    });

    // 创建客户端
    this.client = new Client({
      name: 'create-p2-client',
      version: '1.0.0'
    }, {
      capabilities: {}
    });

    // 连接到服务器
    await this.client.connect(this.transport);
    console.log('✅ MCP 客户端连接成功');
  }

  async sendRequest(method, params) {
    if (!this.client) {
      throw new Error('MCP 客户端未连接');
    }
    return await this.client.request(params, method);
  }

  async disconnect() {
    if (this.client) {
      await this.client.close();
    }
  }
}

// 生成唯一 UID（模拟 NocoBase 的 uid 函数）
function generateUID() {
  let IDX = 36, HEX = '';
  while (IDX--) HEX += IDX.toString(36);
  
  let str = '', num = 11;
  while (num--) str += HEX[(Math.random() * 36) | 0];
  return str;
}

async function main() {
  const mcpClient = new McpClient();
  
  try {
    console.log('🚀 开始使用 MCP 工具创建 p2 页面菜单并添加 markdown 区块\n');
    
    // 连接 MCP 客户端
    await mcpClient.connect();
    
    // 1. 生成页面 Schema UID
    const pageSchemaUid = generateUID();
    console.log(`📋 生成页面 Schema UID: ${pageSchemaUid}`);
    
    // 2. 创建 p2 页面菜单
    console.log('\n📄 步骤 1: 创建 p2 页面菜单');
    const createPageResult = await mcpClient.sendRequest('tools/call', {
      name: 'create_menu_page',
      arguments: {
        title: 'p2',
        schemaUid: pageSchemaUid,
        icon: 'file-text',
        enableTabs: false,
        enableHeader: true
      }
    });
    
    console.log('✅ p2 页面菜单创建成功:');
    console.log(createPageResult.content[0].text);
    
    // 等待页面创建完成
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 3. 在 p2 页面中添加 markdown 区块
    console.log('\n📝 步骤 2: 在 p2 页面中添加 markdown 区块');
    const addMarkdownResult = await mcpClient.sendRequest('tools/call', {
      name: 'add_markdown_block_smart',
      arguments: {
        pageSchemaUid: pageSchemaUid,
        title: 'Welcome to p2',
        content: '# 🎉 欢迎来到 p2 页面\n\n这是一个使用 **MCP 工具** 创建的页面和 markdown 区块！\n\n## ✨ 功能特性\n\n- ✅ 通过 MCP 工具自动创建\n- ✅ 支持完整的 Markdown 语法\n- ✅ 可以随时编辑内容\n- ✅ 响应式设计\n\n## 🚀 技术栈\n\n- **NocoBase**: 低代码开发平台\n- **MCP**: Model Context Protocol\n- **Markdown**: 轻量级标记语言\n\n---\n\n*此区块由 MCP 工具自动生成于 ' + new Date().toLocaleString('zh-CN') + '*',
        position: 'beforeEnd'
      }
    });
    
    console.log('✅ Markdown 区块添加成功:');
    console.log(addMarkdownResult.content[0].text);
    
    console.log('\n🎯 任务完成！');
    console.log('📱 请在浏览器中查看新创建的 p2 页面:');
    console.log('🔗 http://nocobase.nocobasedocker.orb.local/apps/mcp_playground/admin');
    
  } catch (error) {
    console.error('❌ 执行失败:', error.message);
    if (error.response) {
      console.error('响应数据:', JSON.stringify(error.response, null, 2));
    }
  } finally {
    await mcpClient.disconnect();
  }
}

// 运行主函数
main().catch(console.error);
