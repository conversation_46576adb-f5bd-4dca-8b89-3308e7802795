#!/usr/bin/env node

/**
 * 测试修复后的 Markdown block（使用正确的 Grid 结构）
 */

import { NocoBaseClient } from './dist/client.js';
import { handleAddMarkdownBlock } from './dist/tools/blocks.js';

// 配置 NocoBase 客户端
const client = new NocoBaseClient({
  baseUrl: 'https://app.dev.orb.local/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
  app: 'mcp_playground'
});

async function main() {
  try {
    console.log('🧪 测试修复后的 Markdown block（使用正确的 Grid 结构）...');
    
    // Tab UID
    const tabUid = 'szdigp6b1ug';
    
    // 添加 Markdown block（使用修复后的 Grid 结构）
    console.log('📝 添加 Markdown block（Grid.Row -> Grid.Col -> Markdown.Void）...');
    const markdownResult = await handleAddMarkdownBlock(client, {
      parentUid: tabUid,
      content: '# 🎯 Final Test - Grid Structure Fixed!\n\nThis Markdown block uses the **correct Grid structure**:\n\n```\nGrid -> Grid.Row -> Grid.Col -> Markdown.Void\n```\n\n## ✅ What was fixed:\n\n1. **Proper Grid hierarchy** - Now wrapped in Grid.Row and Grid.Col\n2. **Correct decorator usage** - Using `x-decorator: "CardItem"`\n3. **All required properties** - version, x-app-version, _isJSONSchemaObject\n4. **Proper component structure** - Matches manual creation pattern\n\n---\n\n**This should now display correctly in the browser!** 🎉\n\n*Created via MCP with the fully corrected template.*',
      position: 'beforeEnd'
    });
    
    console.log('✅ Markdown block 添加成功!');
    console.log(markdownResult.content[0].text);
    
  } catch (error) {
    console.error('❌ 错误:', error.message);
    console.error(error.stack);
  }
}

main();
