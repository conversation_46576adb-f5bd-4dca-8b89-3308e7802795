#!/usr/bin/env node

/**
 * 测试通过 MCP 添加 Users collection 的 table
 */

import { NocoBaseClient } from './dist/client.js';
import { handleAddTableBlock } from './dist/tools/blocks.js';

// 配置 NocoBase 客户端
const client = new NocoBaseClient({
  baseUrl: 'https://app.dev.orb.local/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
  app: 'mcp_playground'
});

async function main() {
  try {
    console.log('🧪 测试通过 MCP 添加 Users collection 的 table...');
    
    // Tab UID
    const tabUid = 'szdigp6b1ug';
    
    // 添加 Users Table block（使用修复后的模板）
    console.log('📊 添加 Users Table block（使用修复后的 Grid 结构）...');
    const tableResult = await handleAddTableBlock(client, {
      parentUid: tabUid,
      collectionName: 'users',
      dataSource: 'main',
      title: 'Users Management',
      position: 'beforeEnd'
    });
    
    console.log('✅ Users Table block 添加成功!');
    console.log(tableResult.content[0].text);
    
    // 等待一下，然后检查 schema
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('🔍 检查添加后的 tab schema...');
    const tabSchema = await client.getUISchema(tabUid);
    
    // 查找我们刚添加的 table block
    const findTableBlock = (obj, path = '') => {
      if (obj && typeof obj === 'object') {
        if (obj['x-decorator'] === 'TableBlockProvider' && obj['x-decorator-props']?.collection === 'users') {
          console.log(`🎯 找到 Users Table block: ${obj['x-uid']} (路径: ${path})`);
          console.log('📋 Table block 配置:', JSON.stringify(obj['x-decorator-props'], null, 2));
          return obj;
        }
        
        if (obj.properties) {
          for (const [key, value] of Object.entries(obj.properties)) {
            const result = findTableBlock(value, `${path}.properties.${key}`);
            if (result) return result;
          }
        }
      }
      return null;
    };
    
    const tableBlock = findTableBlock(tabSchema);
    if (tableBlock) {
      console.log('✅ Users Table block 已成功添加到 schema 中!');
    } else {
      console.log('⚠️ 未在 schema 中找到 Users Table block');
    }
    
  } catch (error) {
    console.error('❌ 错误:', error.message);
    console.error(error.stack);
  }
}

main();
