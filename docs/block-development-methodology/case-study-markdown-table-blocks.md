# 案例研究：Markdown 和 Table Block 修复过程

## 📋 概述

本案例研究详细记录了使用 Playwright API 监测方法修复 NocoBase MCP 工具中 Markdown 和 Table Block 创建问题的完整过程。

## 🎯 问题描述

### 初始问题
- MCP 工具创建的 Markdown block 不在浏览器中显示
- Table block 创建功能尚未实现
- 生成的 schema 结构与手动创建的不一致

### 预期目标
- Markdown block 在浏览器中正确显示
- Table block 功能完全实现
- 生成的 schema 与手动创建的完全一致

## 🔍 Markdown Block 修复过程

### 步骤 1: 手动操作监测

#### 1.1 环境准备
```bash
# 启动 MCP 服务器
npm run start -- --base-url "https://app.dev.orb.local/api" \
  --token "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  --app "mcp_playground"

# 导航到测试页面
browser_navigate_Playwright({
  url: "https://app.dev.orb.local/apps/mcp_playground/admin/jf8515glj1b"
})
```

#### 1.2 手动创建 Markdown Block
```javascript
// 1. 点击 "Add block" 按钮
browser_click_Playwright({ element: "Add block 按钮", ref: "e151" })

// 2. 选择 "Markdown" 选项
browser_click_Playwright({ element: "Markdown 菜单项", ref: "e277" })

// 3. 系统自动创建 block
```

#### 1.3 捕获 API 请求
```javascript
const requests = await browser_network_requests_Playwright();

// 关键发现：
// POST /api/uiSchemas:insertAdjacent/szdigp6b1ug?position=beforeEnd
```

### 步骤 2: Schema 结构分析

#### 2.1 手动创建的 Schema
```json
{
  "type": "void",
  "version": "2.0",
  "x-editable": false,
  "x-settings": "blockSettings:markdown",
  "x-component": "Markdown.Void",
  "x-decorator": "CardItem",
  "x-app-version": "1.8.14",
  "x-component-props": {
    "content": "This is a demo text, **supports Markdown syntax**."
  },
  "x-decorator-props": {
    "name": "markdown",
    "engine": "handlebars"
  },
  "_isJSONSchemaObject": true
}
```

#### 2.2 原始 MCP 模板
```json
{
  "type": "void",
  "x-component": "CardItem",  // ❌ 错误
  "x-component-props": {
    "title": "..."
  },
  "properties": {
    "innerUid": {
      "type": "void",
      "x-component": "Markdown.Void",  // ❌ 嵌套错误
      "x-component-props": {
        "content": "..."
      }
    }
  }
}
```

### 步骤 3: 关键差异识别

#### 3.1 组件结构问题
- **错误**: `x-component: "CardItem"` + 嵌套的 `Markdown.Void`
- **正确**: `x-decorator: "CardItem"` + 直接的 `Markdown.Void`

#### 3.2 缺失属性
- `version: "2.0"`
- `x-app-version: "1.8.14"`
- `_isJSONSchemaObject: true`
- `x-decorator-props`

#### 3.3 Grid 结构问题
- **错误**: 直接在 Grid 下添加 block
- **正确**: Grid -> Grid.Row -> Grid.Col -> Block

### 步骤 4: 模板修复

#### 4.1 第一次修复（组件结构）
```typescript
export const createMarkdownBlockSchema = (options) => {
  return {
    type: 'void',
    version: '2.0',
    'x-editable': false,
    'x-settings': 'blockSettings:markdown',
    'x-component': 'Markdown.Void',      // ✅ 修复
    'x-decorator': 'CardItem',           // ✅ 修复
    'x-app-version': '1.8.14',          // ✅ 添加
    'x-component-props': { content },
    'x-decorator-props': {               // ✅ 添加
      name: 'markdown',
      engine: 'handlebars'
    },
    '_isJSONSchemaObject': true,         // ✅ 添加
    'x-async': false,
    'x-index': 1
  };
};
```

#### 4.2 第二次修复（Grid 结构）
```typescript
export const createMarkdownBlockSchema = (options) => {
  const rowUid = generateUid();
  const colUid = generateUid();
  const blockUid = generateUid();

  return {
    type: 'void',
    version: '2.0',
    'x-component': 'Grid.Row',           // ✅ Grid 结构
    'x-app-version': '1.8.14',
    '_isJSONSchemaObject': true,
    properties: {
      [colUid]: {
        type: 'void',
        version: '2.0',
        'x-component': 'Grid.Col',       // ✅ Grid 结构
        properties: {
          [blockUid]: {
            type: 'void',
            version: '2.0',
            'x-decorator': 'CardItem',   // ✅ 正确结构
            'x-component': 'Markdown.Void',
            // ... 其他属性
          }
        }
      }
    }
  };
};
```

### 步骤 5: 验证结果

#### 5.1 测试脚本
```javascript
const result = await handleAddMarkdownBlock(client, {
  parentUid: 'szdigp6b1ug',
  content: '# 🎯 Final Test - Grid Structure Fixed!',
  position: 'beforeEnd'
});
```

#### 5.2 浏览器验证
- ✅ Block 在页面中正确显示
- ✅ Markdown 内容正确渲染
- ✅ 样式与手动创建的一致

## 🔧 Table Block 实现过程

### 步骤 1: 手动操作监测

#### 1.1 手动创建 Table Block
```javascript
// 1. 点击 "Add block" -> "Table"
browser_click_Playwright({ element: "Table 菜单项", ref: "e174" })

// 2. 选择 "Users" collection
browser_click_Playwright({ element: "Users 菜单项", ref: "e306" })

// 3. 系统自动创建 table
```

#### 1.2 捕获关键 API
```javascript
// 创建 Table block
POST /api/uiSchemas:insertAdjacent/szdigp6b1ug?position=beforeEnd

// 加载数据
GET /api/users:list?pageSize=20&filter=%7B%7D
```

### 步骤 2: Table Schema 分析

#### 2.1 完整的 Table 结构
```json
{
  "type": "void",
  "version": "2.0",
  "x-toolbar": "BlockSchemaToolbar",
  "x-settings": "blockSettings:table",
  "x-component": "CardItem",
  "x-decorator": "TableBlockProvider",     // ✅ 关键
  "x-acl-action": "users:list",
  "x-decorator-props": {
    "action": "list",
    "params": { "pageSize": 20 },
    "dragSort": false,
    "showIndex": true,
    "collection": "users",
    "dataSource": "main"
  },
  "properties": {
    "actions": {
      "x-component": "ActionBar",
      "x-initializer": "table:configureActions"
    },
    "tableComponent": {
      "type": "array",
      "x-component": "TableV2",
      "x-initializer": "table:configureColumns",
      "properties": {
        "actions": {
          "x-component": "TableV2.Column",
          "x-decorator": "TableV2.Column.ActionBar"
        }
      }
    }
  }
}
```

### 步骤 3: Table 模板实现

#### 3.1 完整的 Table 模板
```typescript
export const createTableBlockSchema = (options) => {
  const rowUid = generateUid();
  const colUid = generateUid();
  const blockUid = generateUid();
  const actionsUid = generateUid();
  const tableUid = generateUid();

  return {
    type: 'void',
    version: '2.0',
    'x-component': 'Grid.Row',
    properties: {
      [colUid]: {
        type: 'void',
        'x-component': 'Grid.Col',
        properties: {
          [blockUid]: {
            type: 'void',
            'x-component': 'CardItem',
            'x-decorator': 'TableBlockProvider',  // ✅ 关键
            'x-acl-action': `${collectionName}:list`,
            'x-decorator-props': {
              action: 'list',
              params: { pageSize: 20 },
              collection: collectionName,
              dataSource
            },
            properties: {
              [actionsUid]: {
                'x-component': 'ActionBar',
                'x-initializer': 'table:configureActions'
              },
              [tableUid]: {
                type: 'array',
                'x-component': 'TableV2',
                'x-initializer': 'table:configureColumns',
                properties: {
                  actions: {
                    'x-component': 'TableV2.Column',
                    'x-decorator': 'TableV2.Column.ActionBar'
                  }
                }
              }
            }
          }
        }
      }
    }
  };
};
```

### 步骤 4: 验证结果

#### 4.1 测试脚本
```javascript
const tableResult = await handleAddTableBlock(client, {
  parentUid: 'szdigp6b1ug',
  collectionName: 'users',
  dataSource: 'main',
  title: 'Users Management',
  position: 'beforeEnd'
});
```

#### 4.2 浏览器验证
- ✅ Table 在页面中正确显示
- ✅ 用户数据正确加载（显示 "Total 3 items"）
- ✅ 所有功能正常（分页、选择、配置等）

## 📊 成果总结

### 修复前后对比

| 方面 | 修复前 | 修复后 |
|------|--------|--------|
| Markdown 显示 | ❌ 不显示 | ✅ 正确显示 |
| Table 功能 | ❌ 未实现 | ✅ 完全实现 |
| Schema 结构 | ❌ 不一致 | ✅ 完全一致 |
| Grid 层次 | ❌ 错误 | ✅ 正确 |
| 组件属性 | ❌ 缺失 | ✅ 完整 |

### 关键学习点

1. **API 监测的重要性**: 通过监测手动操作，我们发现了正确的 API 调用模式
2. **Schema 结构的复杂性**: NocoBase 的 schema 结构比预期更复杂，需要精确匹配
3. **Grid 层次的必要性**: 所有 block 都必须包装在正确的 Grid 结构中
4. **组件vs装饰器**: 理解 `x-component` 和 `x-decorator` 的区别至关重要

### 可复用的方法

这个修复过程建立了一个可复用的方法论：
1. 手动操作监测
2. API 请求分析
3. Schema 结构对比
4. 模板逐步修复
5. 全面验证测试

这个方法可以应用于修复其他 Block 类型（Form、Details、List 等）。

## 🎯 后续改进建议

1. **自动化测试**: 为每个 Block 类型创建自动化测试
2. **模板验证器**: 开发工具自动验证生成的 schema
3. **文档完善**: 为每个 Block 类型创建详细的实现文档
4. **错误处理**: 改进错误处理和用户反馈机制

这个案例研究展示了系统性方法在解决复杂技术问题中的有效性，为未来的开发工作提供了宝贵的经验和可复用的流程。

## 📚 相关文档

- [方法论概述](./README.md)
- [Block 开发工作流程](./block-development-workflow.md)
- [API 监测技术指南](./api-monitoring-guide.md)
- [快速参考指南](./quick-reference-guide.md)
