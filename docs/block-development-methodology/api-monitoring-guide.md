# NocoBase API 监测技术指南

## 📋 概述

本指南详细说明如何使用 Playwright 监测 NocoBase 手动操作的 API 调用，以优化 MCP 工具的 Block 创建模板。

## 🔧 技术实现

### 1. Playwright 网络监测设置

#### 1.1 基础监测
```javascript
// 开始监测网络请求
const requests = await browser_network_requests_Playwright();

// 执行手动操作...

// 获取所有请求
const allRequests = await browser_network_requests_Playwright();
```

#### 1.2 关键 API 模式识别
```javascript
// NocoBase Block 创建的关键 API 模式：
const blockCreationAPIs = [
  'POST /api/uiSchemas:insertAdjacent/{parentUid}?position=beforeEnd',
  'GET /api/{collection}:list',
  'GET /api/uiSchemas:getJsonSchema/{schemaUid}',
  'POST /api/collections:listMeta'
];
```

### 2. Schema 结构分析

#### 2.1 手动创建的 Markdown Block Schema
```json
{
  "type": "void",
  "version": "2.0",
  "x-uid": "unique-id",
  "x-editable": false,
  "x-settings": "blockSettings:markdown",
  "x-component": "Markdown.Void",
  "x-decorator": "CardItem",
  "x-app-version": "1.8.14",
  "x-component-props": {
    "content": "This is a demo text, **supports Markdown syntax**."
  },
  "x-decorator-props": {
    "name": "markdown",
    "engine": "handlebars"
  },
  "_isJSONSchemaObject": true,
  "x-async": false,
  "x-index": 1
}
```

#### 2.2 手动创建的 Table Block Schema
```json
{
  "type": "void",
  "version": "2.0",
  "x-uid": "unique-id",
  "x-toolbar": "BlockSchemaToolbar",
  "x-settings": "blockSettings:table",
  "x-component": "CardItem",
  "x-decorator": "TableBlockProvider",
  "x-acl-action": "users:list",
  "x-app-version": "1.8.14",
  "x-filter-targets": [],
  "x-decorator-props": {
    "action": "list",
    "params": { "pageSize": 20 },
    "dragSort": false,
    "showIndex": true,
    "collection": "users",
    "dataSource": "main"
  },
  "_isJSONSchemaObject": true,
  "x-use-decorator-props": "useTableBlockDecoratorProps",
  "properties": {
    "actions": {
      "type": "void",
      "x-component": "ActionBar",
      "x-initializer": "table:configureActions"
    },
    "tableComponent": {
      "type": "array",
      "x-component": "TableV2",
      "x-initializer": "table:configureColumns",
      "properties": {
        "actions": {
          "x-component": "TableV2.Column",
          "x-decorator": "TableV2.Column.ActionBar"
        }
      }
    }
  }
}
```

### 3. Grid 层次结构分析

#### 3.1 正确的 Grid 结构
```json
{
  "Grid": {
    "properties": {
      "Grid.Row": {
        "type": "void",
        "version": "2.0",
        "x-component": "Grid.Row",
        "x-app-version": "1.8.14",
        "_isJSONSchemaObject": true,
        "properties": {
          "Grid.Col": {
            "type": "void",
            "version": "2.0",
            "x-component": "Grid.Col",
            "x-app-version": "1.8.14",
            "_isJSONSchemaObject": true,
            "properties": {
              "Block": {
                "type": "void",
                "version": "2.0",
                "x-decorator": "CardItem",
                "x-component": "Markdown.Void",
                // ... block specific properties
              }
            }
          }
        }
      }
    }
  }
}
```

### 4. API 请求分析工具

#### 4.1 请求过滤器
```javascript
function filterBlockCreationRequests(requests) {
  return requests.filter(req => {
    const url = req.url;
    const method = req.method;
    
    // Block 创建相关的 API
    if (method === 'POST' && url.includes('uiSchemas:insertAdjacent')) {
      return true;
    }
    
    // 数据加载相关的 API
    if (method === 'GET' && url.includes(':list')) {
      return true;
    }
    
    // Schema 获取相关的 API
    if (method === 'GET' && url.includes('uiSchemas:getJsonSchema')) {
      return true;
    }
    
    return false;
  });
}
```

#### 4.2 Schema 差异分析器
```javascript
function analyzeSchemaChanges(beforeSchema, afterSchema) {
  const changes = {
    added: [],
    modified: [],
    structure: {}
  };
  
  // 递归比较 schema 结构
  function compareObjects(before, after, path = '') {
    for (const key in after) {
      const currentPath = path ? `${path}.${key}` : key;
      
      if (!(key in before)) {
        changes.added.push({
          path: currentPath,
          value: after[key]
        });
      } else if (typeof after[key] === 'object' && after[key] !== null) {
        compareObjects(before[key], after[key], currentPath);
      } else if (before[key] !== after[key]) {
        changes.modified.push({
          path: currentPath,
          before: before[key],
          after: after[key]
        });
      }
    }
  }
  
  compareObjects(beforeSchema, afterSchema);
  return changes;
}
```

### 5. 模板生成器

#### 5.1 基于监测结果的模板生成
```typescript
interface BlockTemplate {
  type: string;
  version: string;
  'x-component'?: string;
  'x-decorator'?: string;
  'x-app-version': string;
  '_isJSONSchemaObject': boolean;
  properties?: Record<string, any>;
}

function generateTemplateFromMonitoring(
  monitoredSchema: any,
  blockType: string
): BlockTemplate {
  const template: BlockTemplate = {
    type: 'void',
    version: '2.0',
    'x-app-version': '1.8.14',
    '_isJSONSchemaObject': true
  };
  
  // 提取关键属性
  if (monitoredSchema['x-component']) {
    template['x-component'] = monitoredSchema['x-component'];
  }
  
  if (monitoredSchema['x-decorator']) {
    template['x-decorator'] = monitoredSchema['x-decorator'];
  }
  
  // 提取 properties
  if (monitoredSchema.properties) {
    template.properties = monitoredSchema.properties;
  }
  
  return template;
}
```

### 6. 验证工具

#### 6.1 Schema 验证器
```javascript
function validateGeneratedSchema(generated, reference) {
  const issues = [];
  
  // 检查必需属性
  const requiredProps = [
    'type', 'version', 'x-app-version', '_isJSONSchemaObject'
  ];
  
  for (const prop of requiredProps) {
    if (!(prop in generated)) {
      issues.push(`Missing required property: ${prop}`);
    }
  }
  
  // 检查组件结构
  if (reference['x-decorator'] && !generated['x-decorator']) {
    issues.push('Missing x-decorator property');
  }
  
  if (reference['x-component'] && !generated['x-component']) {
    issues.push('Missing x-component property');
  }
  
  return issues;
}
```

#### 6.2 功能测试器
```javascript
async function testBlockFunctionality(client, blockUid) {
  try {
    // 测试 block 是否正确创建
    const schema = await client.getUISchema(blockUid);
    
    // 测试 block 是否可见
    const isVisible = await browser_evaluate_Playwright({
      function: `() => {
        const element = document.querySelector('[data-uid="${blockUid}"]');
        return element && element.offsetParent !== null;
      }`
    });
    
    return {
      schemaExists: !!schema,
      isVisible: isVisible,
      hasCorrectStructure: validateBlockStructure(schema)
    };
  } catch (error) {
    return {
      error: error.message,
      schemaExists: false,
      isVisible: false
    };
  }
}
```

## 🎯 监测检查清单

### Block 创建监测
- [ ] 捕获 `uiSchemas:insertAdjacent` API 调用
- [ ] 记录完整的请求体 schema
- [ ] 获取创建前后的父容器 schema
- [ ] 识别新增的 block 结构

### Schema 结构验证
- [ ] 确认 Grid 层次结构 (Grid -> Grid.Row -> Grid.Col -> Block)
- [ ] 验证组件和装饰器属性 (`x-component`, `x-decorator`)
- [ ] 检查版本属性 (`version`, `x-app-version`)
- [ ] 确认元数据属性 (`_isJSONSchemaObject`)

### 功能测试
- [ ] Block 在浏览器中正确显示
- [ ] 所有交互功能正常工作
- [ ] 数据正确加载（对于数据 blocks）
- [ ] 配置选项可用

### 模板优化
- [ ] 更新 block 模板代码
- [ ] 重新构建项目
- [ ] 运行自动化测试
- [ ] 验证修复结果

## 📊 常见问题和解决方案

### 问题 1: Block 不显示
**原因**: 缺少正确的 Grid 结构
**解决**: 确保 block 包装在 Grid.Row -> Grid.Col 中

### 问题 2: 样式不正确
**原因**: 使用了错误的组件/装饰器组合
**解决**: 使用 `x-decorator: "CardItem"` 而不是 `x-component: "CardItem"`

### 问题 3: 功能缺失
**原因**: 缺少必需的属性或初始化器
**解决**: 对比手动创建的完整 schema，添加缺失的属性

### 问题 4: 数据不加载
**原因**: 缺少正确的 decorator props 或 ACL 配置
**解决**: 确保 `x-decorator-props` 和 `x-acl-action` 正确配置

这个技术指南提供了完整的 API 监测和模板优化方法，确保 MCP 工具能够生成与 NocoBase 原生创建完全一致的 Block。
