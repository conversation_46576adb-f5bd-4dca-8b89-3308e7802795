# NocoBase MCP Block 开发快速参考

## 🚀 关键 API 端点

### Block 创建
```http
POST /api/uiSchemas:insertAdjacent/{parentUid}?position=beforeEnd
Content-Type: application/json

{
  "type": "void",
  "version": "2.0",
  "x-component": "Grid.Row",
  "properties": { ... }
}
```

### Schema 获取
```http
GET /api/uiSchemas:getJsonSchema/{schemaUid}
```

### 数据加载（Table blocks）
```http
GET /api/{collection}:list?pageSize=20&filter=%7B%7D
```

## 📋 Schema 模式

### 基础 Block 结构
```json
{
  "type": "void",
  "version": "2.0",
  "x-uid": "unique-id",
  "x-component": "Grid.Row",
  "x-app-version": "1.8.14",
  "_isJSONSchemaObject": true,
  "x-async": false,
  "x-index": 1,
  "properties": {
    "colUid": {
      "type": "void",
      "version": "2.0",
      "x-component": "Grid.Col",
      "properties": {
        "blockUid": {
          "type": "void",
          "version": "2.0",
          "x-decorator": "CardItem",
          "x-component": "BlockComponent"
        }
      }
    }
  }
}
```

### Markdown Block
```json
{
  "x-decorator": "CardItem",
  "x-component": "Markdown.Void",
  "x-settings": "blockSettings:markdown",
  "x-component-props": {
    "content": "# Markdown content"
  },
  "x-decorator-props": {
    "name": "markdown",
    "engine": "handlebars"
  }
}
```

### Table Block
```json
{
  "x-decorator": "TableBlockProvider",
  "x-component": "CardItem",
  "x-settings": "blockSettings:table",
  "x-acl-action": "collection:list",
  "x-decorator-props": {
    "action": "list",
    "params": { "pageSize": 20 },
    "collection": "collectionName",
    "dataSource": "main",
    "showIndex": true,
    "dragSort": false
  },
  "properties": {
    "actions": {
      "x-component": "ActionBar",
      "x-initializer": "table:configureActions"
    },
    "table": {
      "type": "array",
      "x-component": "TableV2",
      "x-initializer": "table:configureColumns"
    }
  }
}
```

## 🛠️ 常用工具函数

### Playwright 操作
```javascript
// 导航到页面
await browser_navigate_Playwright({
  url: "https://app.dev.orb.local/apps/mcp_playground/admin/{pageUid}"
});

// 点击元素
await browser_click_Playwright({
  element: "Add block 按钮",
  ref: "elementRef"
});

// 获取网络请求
const requests = await browser_network_requests_Playwright();

// 页面快照
await browser_snapshot_Playwright();
```

### MCP 工具调用
```javascript
// 添加 Markdown block
const result = await handleAddMarkdownBlock(client, {
  parentUid: 'parentSchemaUid',
  content: '# Markdown content',
  position: 'beforeEnd'
});

// 添加 Table block
const result = await handleAddTableBlock(client, {
  parentUid: 'parentSchemaUid',
  collectionName: 'users',
  dataSource: 'main',
  position: 'beforeEnd'
});
```

## ✅ 检查清单

### Block 创建前
- [ ] 确认父容器 UID
- [ ] 验证 collection 存在（数据 blocks）
- [ ] 检查用户权限

### Schema 验证
- [ ] `type: "void"`
- [ ] `version: "2.0"`
- [ ] `x-app-version: "1.8.14"`
- [ ] `_isJSONSchemaObject: true`
- [ ] 正确的 Grid 结构
- [ ] 适当的 `x-decorator` 和 `x-component`

### 浏览器测试
- [ ] Block 正确显示
- [ ] 样式与手动创建一致
- [ ] 功能正常工作
- [ ] 数据正确加载（数据 blocks）

## 🐛 常见问题

### Block 不显示
**检查**: Grid 结构是否正确
```json
Grid -> Grid.Row -> Grid.Col -> Block
```

### 样式错误
**检查**: 组件/装饰器配置
```json
{
  "x-decorator": "CardItem",  // ✅ 正确
  "x-component": "Markdown.Void"
}
```

### 数据不加载
**检查**: ACL 和 decorator props
```json
{
  "x-acl-action": "collection:list",
  "x-decorator-props": {
    "collection": "collectionName",
    "action": "list"
  }
}
```

## 📊 Block 类型映射

| Block 类型 | x-decorator | x-component | x-settings |
|------------|-------------|-------------|------------|
| Markdown | CardItem | Markdown.Void | blockSettings:markdown |
| Table | TableBlockProvider | CardItem | blockSettings:table |
| Form | FormBlockProvider | CardItem | blockSettings:createForm |
| Details | DetailsBlockProvider | CardItem | blockSettings:details |
| List | ListBlockProvider | CardItem | blockSettings:list |

## 🔧 开发环境

### 测试环境配置
```bash
# MCP 服务器启动
npm run start -- \
  --base-url "https://app.dev.orb.local/api" \
  --token "YOUR_TOKEN" \
  --app "mcp_playground"
```

### 测试页面
- **Admin URL**: `https://app.dev.orb.local/apps/mcp_playground/admin`
- **Test Page**: `https://app.dev.orb.local/apps/mcp_playground/admin/jf8515glj1b`
- **Tab UID**: `szdigp6b1ug`

### 认证信息
- **Username**: `neo`
- **Password**: `neo@123`
- **Token**: 见环境配置

## 📚 相关文档

- [完整工作流程](./block-development-workflow.md)
- [技术实现指南](./api-monitoring-guide.md)
- [案例研究](./case-study-markdown-table-blocks.md)

## 🎯 下一步

1. **扩展其他 Block 类型**: Form, Details, List, Calendar 等
2. **自动化测试**: 为每个 Block 类型创建测试套件
3. **错误处理**: 改进错误处理和用户反馈
4. **性能优化**: 优化 schema 生成和验证过程
