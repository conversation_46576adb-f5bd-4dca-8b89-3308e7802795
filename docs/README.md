# NocoBase MCP 文档索引

## 🚀 Block 开发方法论

基于 Playwright 监测的 Block 开发完整工作流程：

- **[📁 Block 开发方法论文档集](./block-development-methodology/README.md)**:
  完整的方法论文档集
  - [Block 开发工作流程](./block-development-methodology/block-development-workflow.md):
    完整的开发流程指南
  - [API 监测技术指南](./block-development-methodology/api-monitoring-guide.md):
    深入的技术实现细节
  - [案例研究：Markdown 和 Table Block 修复](./block-development-methodology/case-study-markdown-table-blocks.md):
    实际修复过程记录
  - [快速参考指南](./block-development-methodology/quick-reference-guide.md):
    开发过程中的快速查询手册

## 📚 原有文档

### 介绍与使用

- [USAGE_EXAMPLES.md](./USAGE_EXAMPLES.md): 使用示例汇总
- [how_to_build_mcp.md](./how_to_build_mcp.md): 如何构建 MCP

### 模块文档

- [table-operations](./table-operations/README.md): 表格操作
- [kanban-operations](./kanban-operations/README.md): 看板操作
- [row-actions](./row-actions/README.md): 行操作
- [mcp](./mcp/README.md): MCP 相关文档
- [block-api-mappings](./block-api-mappings/README.md): NocoBase 区块 API 映射

### 参考与指南

- [COLLECTION_CREATION_GUIDE.md](./COLLECTION_CREATION_GUIDE.md)
- [COLLECTION_CATEGORIES_GUIDE.md](./COLLECTION_CATEGORIES_GUIDE.md)
- [nocobase_collections_api_reference.md](./nocobase_collections_api_reference.md)
- [nocobase_collections_api_quick_reference.md](./nocobase_collections_api_quick_reference.md)
- [nocobase_collection_default_fields.md](./nocobase_collection_default_fields.md)
- [nocobase_collection_api.md](./nocobase_collection_api.md)

## 🎯 推荐阅读路径

### 新手入门

1. [Block 开发方法论概述](./block-development-methodology/README.md) -
   了解整体方法论
2. [Block 开发工作流程](./block-development-methodology/block-development-workflow.md) -
   掌握开发流程
3. [快速参考指南](./block-development-methodology/quick-reference-guide.md) -
   掌握基础操作

### 深入开发

1. [API 监测技术指南](./block-development-methodology/api-monitoring-guide.md) -
   掌握技术细节
2. [案例研究](./block-development-methodology/case-study-markdown-table-blocks.md) -
   学习实际案例
3. [USAGE_EXAMPLES.md](./USAGE_EXAMPLES.md) - 查看更多示例
